# USe the bronze_layer to get expenses df and budget df
import pandas as pd
import sqlite3
import os
from bronze_layer import bronze_expenses, bronze_budget
from get_latest_exchange_rate import get_latest_exchange_rate
from pandas.tseries.offsets import MonthEnd

DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"


def silver_budget() -> pd.DataFrame:
    """Returns a clean budget dataframe."""
    budget_df = bronze_budget()
    # modify KEO2 to KE02 in bronze budget df
    budget_df['project_id'] = budget_df['project_id'].replace('KEO2', 'KE02')

    # drop version column, id column
    budget_df.drop(columns=['version', 'id'], inplace=True)

    # Create a date column:
    # convert the 'year' and 'month' columns to string format
    budget_df['year'] = budget_df['year'].astype(str)
    budget_df['month'] = budget_df['month'].astype(str)

    # create a temporary date string with the 1st day of the month
    budget_df['temp_date_str'] = budget_df['year'] + '-' + budget_df['month'].str.zfill(2) + '-01'

    # convert the temporary date string to datetime objects
    budget_df['date'] = pd.to_datetime(budget_df['temp_date_str'], errors='coerce')

    # convert to the last day of the month using MonthEnd
    budget_df['date'] = budget_df['date'] + MonthEnd(1)

    # drop the intermediate columns 
    budget_df = budget_df.drop(columns=['year', 'month', 'temp_date_str'])

    return budget_df


def silver_expenses() -> pd.DataFrame:
    """Returns a clean expenses dataframe."""
    expenses_df = bronze_expenses()
    # Drop id and currency columns
    expenses_df.drop(columns=['id', 'currency'], inplace=True)

    # Create a date column:
    # convert the 'year' and 'month' columns to string format
    expenses_df['year'] = expenses_df['year'].astype(str)
    expenses_df['month'] = expenses_df['month'].astype(str)

    # create a temporary date string with the 1st day of the month
    expenses_df['temp_date_str'] = expenses_df['year'] + '-' + expenses_df['month'].str.zfill(2) + '-01'

    # convert the temporary date string to datetime objects
    expenses_df['date'] = pd.to_datetime(expenses_df['temp_date_str'], errors='coerce')

    # convert to the last day of the month using MonthEnd
    expenses_df['date'] = expenses_df['date'] + MonthEnd(1)

    # drop the intermediate columns 
    expenses_df = expenses_df.drop(columns=['year', 'month', 'temp_date_str'])

    # Drop amount_eur column
    expenses_df.drop(columns=['amount_eur'], inplace=True)

    # Define our rate mapping
    eur_to_eur_rate = 1.0 # EUR to EUR conversion rate is 1
    kes_to_eur_rate = get_latest_exchange_rate("KES", "EUR")
    xof_to_eur_rate = get_latest_exchange_rate("XOF", "EUR")

    rate_mapping = {
        'EUR': eur_to_eur_rate,
        'KES': kes_to_eur_rate,
        'XOF': xof_to_eur_rate
    }

    # Add rate column
    expenses_df['rate'] = expenses_df['original_currency'].map(rate_mapping)

    # Add amount_eur column
    expenses_df['amount_eur'] = expenses_df['amount_local'] * expenses_df['rate']

    return expenses_df

   