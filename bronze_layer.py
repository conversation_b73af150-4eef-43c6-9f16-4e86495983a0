import pandas as pd
import sqlite3
import os

DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"

def bronze_budget()-> pd.DataFrame:
    """Returns bronze budget dataframe, containing all budget data from all CSVs."""

    # This dictionary contains the TRUTH for project country and currency,
    # overriding any inconsistencies found in source data.
    PROJECT_CURRENCY_MAP = {
        "BE01": {"country": "Belgium", "currency": "EUR"},
        "BE55": {"country": "Belgium", "currency": "EUR"},
        "KE01": {"country": "Kenya", "currency": "KES"},   # Corrected: Force KES for KE01 expenses, overriding DB's XOF
        "KEO2": {"country": "Kenya", "currency": "KES"},
        "SN01": {"country": "Senegal", "currency": "XOF"}, # Corrected: Force Senegal for SN01 country, overriding DB's Kenya
        "SN02": {"country": "Senegal", "currency": "XOF"},
        "BF01": {"country": "Burkina Faso", "currency": "XOF"},
        "BF02": {"country": "Burkina Faso", "currency": "XOF"},
    }

    DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
    OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"

    # Read csvs (budget data)
    import pandas as pd
    import os

    # --- Function to extract budget from a single CSV ---
    def extract_budget_from_csv(csv_path: str) -> pd.DataFrame:
        """
        Reads budget data from a CSV file.
        """
        try:
            df = pd.read_csv(csv_path)
            return df
        except Exception as e:
            print(f"Error reading budget from {csv_path}: {e}")
            return pd.DataFrame()
        
    # --- Main logic to iterate and combine CSVs ---
    all_budget_df = pd.DataFrame()
    csv_files_found = 0

    print(f"\n--- Bronze Layer: Starting CSV Data Ingestion from {DATA_DIRECTORY} ---")

    if not os.path.exists(DATA_DIRECTORY):
        print(f"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.")
    else:
        for filename in os.listdir(DATA_DIRECTORY):
            file_path = os.path.join(DATA_DIRECTORY, filename)
            
            # Process only files ending with '_budget.csv' and skip directories
            if os.path.isdir(file_path) or not filename.endswith("_budget.csv"):
                continue

            # Derive project_id from filename (e.g., "BE01" from "BE01_budget.csv")
            project_name_from_file = os.path.splitext(filename)[0]
            print(f"project_name_from_file: {project_name_from_file}")
            base_project_id = project_name_from_file.replace('_budget', '')

            # Get country from our PROJECT_CURRENCY_MAP
            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)
            if not project_info:
                print(f"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.")
                continue
            
            country_from_map = project_info['country']

            print(f"  Ingesting budget from CSV: {filename} (Project: {base_project_id}, Country: {country_from_map})...")
            df = extract_budget_from_csv(file_path)
            # print(f"df: \n{df.head()}")
            
            if not df.empty:
                df['project_id'] = base_project_id
                df['country'] = country_from_map
                # Also add the original_currency_map for consistency, even if budget is already in EUR
                all_budget_df = pd.concat([all_budget_df, df], ignore_index=True)
                csv_files_found += 1
            else:
                print(f" No data extracted or error occurred for {filename}.")

    return all_budget_df

def bronze_expenses()-> pd.DataFrame:
    """Returns a bronze expenses DataFrame, containing all expenses data from all DBs."""
    # --- Bronze Layer: Ingesting Expenses from SQLite DBs ---
    def extract_expenses_from_db(db_path: str) -> pd.DataFrame:
        """
        Connects to a SQLite database and extracts all data from the 'expenses' table.
        """
        conn = None
        try:
            conn = sqlite3.connect(db_path)
            query = "SELECT * FROM expenses" # Assumes 'expenses' is the table name
            df = pd.read_sql_query(query, conn)
            return df
        except sqlite3.Error as e:
            print(f"Error reading expenses from {db_path}: {e}")
            return pd.DataFrame() # Return empty DataFrame on error
        finally:
            if conn:
                conn.close()


    # --- Main logic to iterate and combine DBs ---
    all_expenses_df = pd.DataFrame()
    db_files_found = 0

    # Define project currency map for expenses
    PROJECT_CURRENCY_MAP = {
        "BE01": {"country": "Belgium", "currency": "EUR"},
        "BE55": {"country": "Belgium", "currency": "EUR"},
        "KE01": {"country": "Kenya", "currency": "KES"},
        "KE02": {"country": "Kenya", "currency": "KES"},   
        "SN01": {"country": "Senegal", "currency": "XOF"},
        "SN02": {"country": "Senegal", "currency": "XOF"},
        "BF01": {"country": "Burkina Faso", "currency": "XOF"},
        "BF02": {"country": "Burkina Faso", "currency": "XOF"},
    }

    print(f"\n--- Bronze Layer: Starting DB Data Ingestion from {DATA_DIRECTORY} ---")

    if not os.path.exists(DATA_DIRECTORY):
        print(f"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.")
    else:
        for filename in os.listdir(DATA_DIRECTORY):
            file_path = os.path.join(DATA_DIRECTORY, filename)
            
            # Process only files ending with '.db' and skip directories
            if os.path.isdir(file_path) or not filename.endswith(".db"):
                continue

            # Derive project_id from filename (e.g., "BE01" from "BE01.db")
            project_name_from_file = os.path.splitext(filename)[0]
            base_project_id = project_name_from_file # For DBs, the filename directly is the project_id

            # Get country and currency from our PROJECT_CURRENCY_MAP
            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)
            if not project_info:
                print(f"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.")
                continue
            
            country_from_map = project_info['country']
            currency_from_map = project_info['currency'] # This will be our source of truth for currency

            print(f"  Ingesting expenses from DB: {filename} (Project: {base_project_id}, Country: {country_from_map})...")
            df = extract_expenses_from_db(file_path)
            
            if not df.empty:
                df['project_id'] = base_project_id
                df['country'] = country_from_map
                df['original_currency'] = currency_from_map # Add the true currency from our map
                all_expenses_df = pd.concat([all_expenses_df, df], ignore_index=True)
                db_files_found += 1
            else:
                print(f"   No data extracted or error occurred for {filename}.")

    return all_expenses_df