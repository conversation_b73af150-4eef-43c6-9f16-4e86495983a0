{"cells": [{"cell_type": "code", "execution_count": null, "id": "a18e5138", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f3258897", "metadata": {}, "outputs": [], "source": ["def bronze_budget():\n", "\n", "    # This dictionary contains the TRUTH for project country and currency,\n", "    # overriding any inconsistencies found in source data.\n", "    PROJECT_CURRENCY_MAP = {\n", "        \"BE01\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "        \"BE55\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "        \"KE01\": {\"country\": \"Kenya\", \"currency\": \"KES\"},   # Corrected: Force KES for KE01 expenses, overriding DB's XOF\n", "        \"KEO2\": {\"country\": \"Kenya\", \"currency\": \"KES\"},\n", "        \"SN01\": {\"country\": \"Senegal\", \"currency\": \"XOF\"}, # Corrected: Force Senegal for SN01 country, overriding DB's Kenya\n", "        \"SN02\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "        \"BF01\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "        \"BF02\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "    }\n", "\n", "    DATA_DIRECTORY = \"/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025\"\n", "    OUTPUT_DIRECTORY = \"/Users/<USER>/test/MSF-test/processed_data\"\n", "\n", "    # Read csvs (budget data)\n", "    import pandas as pd\n", "    import os\n", "\n", "    # --- Function to extract budget from a single CSV ---\n", "    def extract_budget_from_csv(csv_path: str) -> pd.DataFrame:\n", "        \"\"\"\n", "        Reads budget data from a CSV file.\n", "        \"\"\"\n", "        try:\n", "            df = pd.read_csv(csv_path)\n", "            return df\n", "        except Exception as e:\n", "            print(f\"Error reading budget from {csv_path}: {e}\")\n", "            return pd.DataFrame()\n", "        \n", "    # --- Main logic to iterate and combine CSVs ---\n", "    all_budget_df = pd.DataFrame()\n", "    csv_files_found = 0\n", "\n", "    print(f\"\\n--- Bronze Layer: Starting CSV Data Ingestion from {DATA_DIRECTORY} ---\")\n", "\n", "    if not os.path.exists(DATA_DIRECTORY):\n", "        print(f\"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.\")\n", "    else:\n", "        for filename in os.listdir(DATA_DIRECTORY):\n", "            file_path = os.path.join(DATA_DIRECTORY, filename)\n", "            \n", "            # Process only files ending with '_budget.csv' and skip directories\n", "            if os.path.isdir(file_path) or not filename.endswith(\"_budget.csv\"):\n", "                continue\n", "\n", "            # Derive project_id from filename (e.g., \"BE01\" from \"BE01_budget.csv\")\n", "            project_name_from_file = os.path.splitext(filename)[0]\n", "            print(f\"project_name_from_file: {project_name_from_file}\")\n", "            base_project_id = project_name_from_file.replace('_budget', '')\n", "\n", "            # Get country from our PROJECT_CURRENCY_MAP\n", "            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)\n", "            if not project_info:\n", "                print(f\"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.\")\n", "                continue\n", "            \n", "            country_from_map = project_info['country']\n", "\n", "            print(f\"  Ingesting budget from CSV: {filename} (Project: {base_project_id}, Country: {country_from_map})...\")\n", "            df = extract_budget_from_csv(file_path)\n", "            # print(f\"df: \\n{df.head()}\")\n", "            \n", "            if not df.empty:\n", "                df['project_id'] = base_project_id\n", "                df['country'] = country_from_map\n", "                # Also add the original_currency_map for consistency, even if budget is already in EUR\n", "                all_budget_df = pd.concat([all_budget_df, df], ignore_index=True)\n", "                csv_files_found += 1\n", "            else:\n", "                print(f\" No data extracted or error occurred for {filename}.\")\n", "\n", "    return all_budget_df\n"]}, {"cell_type": "code", "execution_count": null, "id": "1df5f2d1", "metadata": {}, "outputs": [], "source": ["budget_df = bronze_budget()\n"]}, {"cell_type": "code", "execution_count": null, "id": "6e91a47e", "metadata": {}, "outputs": [], "source": ["budget_df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "01e1d3f9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2e988c31", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlite3\n", "import os\n", "\n", "DATA_DIRECTORY = \"/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025\"\n", "OUTPUT_DIRECTORY = \"/Users/<USER>/test/MSF-test/processed_data\"\n", "\n", "def bronze_expenses()-> pd.DataFrame:\n", "    # --- Bronze Layer: Ingesting Expenses from SQLite DBs ---\n", "    def extract_expenses_from_db(db_path: str) -> pd.DataFrame:\n", "        \"\"\"\n", "        Connects to a SQLite database and extracts all data from the 'expenses' table.\n", "        \"\"\"\n", "        conn = None\n", "        try:\n", "            conn = sqlite3.connect(db_path)\n", "            query = \"SELECT * FROM expenses\" # Assumes 'expenses' is the table name\n", "            df = pd.read_sql_query(query, conn)\n", "            return df\n", "        except sqlite3.Error as e:\n", "            print(f\"Error reading expenses from {db_path}: {e}\")\n", "            return pd.DataFrame() # Return empty DataFrame on error\n", "        finally:\n", "            if conn:\n", "                conn.close()\n", "\n", "\n", "    # --- Main logic to iterate and combine DBs ---\n", "    all_expenses_df = pd.DataFrame()\n", "    db_files_found = 0\n", "\n", "    # Define project currency map for expenses\n", "    PROJECT_CURRENCY_MAP = {\n", "        \"BE01\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "        \"BE55\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "        \"KE01\": {\"country\": \"Kenya\", \"currency\": \"KES\"},\n", "        \"KE02\": {\"country\": \"Kenya\", \"currency\": \"KES\"},   \n", "        \"SN01\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "        \"SN02\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "        \"BF01\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "        \"BF02\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "    }\n", "\n", "    print(f\"\\n--- Bronze Layer: Starting DB Data Ingestion from {DATA_DIRECTORY} ---\")\n", "\n", "    if not os.path.exists(DATA_DIRECTORY):\n", "        print(f\"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.\")\n", "    else:\n", "        for filename in os.listdir(DATA_DIRECTORY):\n", "            file_path = os.path.join(DATA_DIRECTORY, filename)\n", "            \n", "            # Process only files ending with '.db' and skip directories\n", "            if os.path.isdir(file_path) or not filename.endswith(\".db\"):\n", "                continue\n", "\n", "            # Derive project_id from filename (e.g., \"BE01\" from \"BE01.db\")\n", "            project_name_from_file = os.path.splitext(filename)[0]\n", "            base_project_id = project_name_from_file # For DBs, the filename directly is the project_id\n", "\n", "            # Get country and currency from our PROJECT_CURRENCY_MAP\n", "            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)\n", "            if not project_info:\n", "                print(f\"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.\")\n", "                continue\n", "            \n", "            country_from_map = project_info['country']\n", "            currency_from_map = project_info['currency'] # This will be our source of truth for currency\n", "\n", "            print(f\"  Ingesting expenses from DB: {filename} (Project: {base_project_id}, Country: {country_from_map})...\")\n", "            df = extract_expenses_from_db(file_path)\n", "            \n", "            if not df.empty:\n", "                df['project_id'] = base_project_id\n", "                df['country'] = country_from_map\n", "                df['original_currency'] = currency_from_map # Add the true currency from our map\n", "                all_expenses_df = pd.concat([all_expenses_df, df], ignore_index=True)\n", "                db_files_found += 1\n", "            else:\n", "                print(f\"   No data extracted or error occurred for {filename}.\")\n", "\n", "    return all_expenses_df\n"]}, {"cell_type": "code", "execution_count": null, "id": "b2b9b342", "metadata": {}, "outputs": [], "source": ["df = bronze_expenses()"]}, {"cell_type": "code", "execution_count": null, "id": "8231a203", "metadata": {}, "outputs": [], "source": ["\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4164bc22", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e5225136", "metadata": {}, "outputs": [], "source": ["print(\"Hello\")"]}, {"cell_type": "code", "execution_count": null, "id": "4bf7aae7", "metadata": {}, "outputs": [], "source": ["# USe the bronze_layer to get expenses df and budget df\n", "import pandas as pd\n", "import sqlite3\n", "import os\n", "from bronze_layer import bronze_expenses, bronze_budget\n", "\n", "DATA_DIRECTORY = \"/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025\"\n", "OUTPUT_DIRECTORY = \"/Users/<USER>/test/MSF-test/processed_data\"\n", "\n", "expenses_df = bronze_expenses()\n", "budget_df = bronze_budget()"]}, {"cell_type": "code", "execution_count": null, "id": "ca71e64a", "metadata": {}, "outputs": [], "source": ["display(expenses_df.head())\n", "budget_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bbb016f8", "metadata": {}, "outputs": [], "source": ["# USe the bronze_layer to get expenses df and budget df\n", "import pandas as pd\n", "import sqlite3\n", "import os\n", "from bronze_layer import bronze_expenses, bronze_budget\n", "\n", "DATA_DIRECTORY = \"/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025\"\n", "OUTPUT_DIRECTORY = \"/Users/<USER>/test/MSF-test/processed_data\"\n", "\n", "expenses_df = bronze_expenses()\n", "budget_df = bronze_budget()"]}, {"cell_type": "code", "execution_count": null, "id": "210ac274", "metadata": {}, "outputs": [], "source": ["expenses_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "48cb4a63", "metadata": {}, "outputs": [], "source": ["budget_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "bb5836a1", "metadata": {}, "outputs": [], "source": ["# modify KEO1 to KE01 in bronze budget df\n", "budget_df['project_id'] = budget_df['project_id'].replace('KEO1', 'KE01')\n", "budget_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "bb631198", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pandas.tseries.offsets import MonthEnd"]}, {"cell_type": "code", "execution_count": null, "id": "e8d56f77", "metadata": {}, "outputs": [], "source": ["# Create a date column:\n", "# convert the 'year' and 'month' columns to string format\n", "budget_df['year'] = budget_df['year'].astype(str)\n", "budget_df['month'] = budget_df['month'].astype(str)\n", "\n", "# create a temporary date string with the 1st day of the month\n", "budget_df['temp_date_str'] = budget_df['year'] + '-' + budget_df['month'].str.zfill(2) + '-01'\n", "\n", "# convert the temporary date string to datetime objects\n", "budget_df['date'] = pd.to_datetime(budget_df['temp_date_str'], errors='coerce')\n", "\n", "# convert to the last day of the month using MonthEnd\n", "budget_df['date'] = budget_df['date'] + MonthEnd(1)\n", "\n", "# drop the intermediate columns if no longer needed\n", "budget_df = budget_df.drop(columns=['year', 'month', 'temp_date_str'])"]}, {"cell_type": "code", "execution_count": null, "id": "a2b17584", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "962e3098", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d9e733a3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f47b5391", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e9b61790", "metadata": {}, "outputs": [], "source": ["budget_df.info()"]}, {"cell_type": "markdown", "id": "d9a96bcb", "metadata": {}, "source": ["## RESTART"]}, {"cell_type": "code", "execution_count": 1, "id": "8c1e5a9b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlite3\n", "import os\n", "\n", "DATA_DIRECTORY = \"/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025\"\n", "OUTPUT_DIRECTORY = \"/Users/<USER>/test/MSF-test/processed_data\"\n", "\n", "def bronze_budget()-> pd.DataFrame:\n", "    \"\"\"Returns a dataframe containing all budget data from all CSVs.\"\"\"\n", "\n", "    # This dictionary contains the TRUTH for project country and currency,\n", "    # overriding any inconsistencies found in source data.\n", "    PROJECT_CURRENCY_MAP = {\n", "        \"BE01\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "        \"BE55\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "        \"KE01\": {\"country\": \"Kenya\", \"currency\": \"KES\"},   # Corrected: Force KES for KE01 expenses, overriding DB's XOF\n", "        \"KEO2\": {\"country\": \"Kenya\", \"currency\": \"KES\"},\n", "        \"SN01\": {\"country\": \"Senegal\", \"currency\": \"XOF\"}, # Corrected: Force Senegal for SN01 country, overriding DB's Kenya\n", "        \"SN02\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "        \"BF01\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "        \"BF02\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "    }\n", "\n", "    DATA_DIRECTORY = \"/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025\"\n", "    OUTPUT_DIRECTORY = \"/Users/<USER>/test/MSF-test/processed_data\"\n", "\n", "    # Read csvs (budget data)\n", "    import pandas as pd\n", "    import os\n", "\n", "    # --- Function to extract budget from a single CSV ---\n", "    def extract_budget_from_csv(csv_path: str) -> pd.DataFrame:\n", "        \"\"\"\n", "        Reads budget data from a CSV file.\n", "        \"\"\"\n", "        try:\n", "            df = pd.read_csv(csv_path)\n", "            return df\n", "        except Exception as e:\n", "            print(f\"Error reading budget from {csv_path}: {e}\")\n", "            return pd.DataFrame()\n", "        \n", "    # --- Main logic to iterate and combine CSVs ---\n", "    all_budget_df = pd.DataFrame()\n", "    csv_files_found = 0\n", "\n", "    print(f\"\\n--- Bronze Layer: Starting CSV Data Ingestion from {DATA_DIRECTORY} ---\")\n", "\n", "    if not os.path.exists(DATA_DIRECTORY):\n", "        print(f\"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.\")\n", "    else:\n", "        for filename in os.listdir(DATA_DIRECTORY):\n", "            file_path = os.path.join(DATA_DIRECTORY, filename)\n", "            \n", "            # Process only files ending with '_budget.csv' and skip directories\n", "            if os.path.isdir(file_path) or not filename.endswith(\"_budget.csv\"):\n", "                continue\n", "\n", "            # Derive project_id from filename (e.g., \"BE01\" from \"BE01_budget.csv\")\n", "            project_name_from_file = os.path.splitext(filename)[0]\n", "            print(f\"project_name_from_file: {project_name_from_file}\")\n", "            base_project_id = project_name_from_file.replace('_budget', '')\n", "\n", "            # Get country from our PROJECT_CURRENCY_MAP\n", "            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)\n", "            if not project_info:\n", "                print(f\"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.\")\n", "                continue\n", "            \n", "            country_from_map = project_info['country']\n", "\n", "            print(f\"  Ingesting budget from CSV: {filename} (Project: {base_project_id}, Country: {country_from_map})...\")\n", "            df = extract_budget_from_csv(file_path)\n", "            # print(f\"df: \\n{df.head()}\")\n", "            \n", "            if not df.empty:\n", "                df['project_id'] = base_project_id\n", "                df['country'] = country_from_map\n", "                # Also add the original_currency_map for consistency, even if budget is already in EUR\n", "                all_budget_df = pd.concat([all_budget_df, df], ignore_index=True)\n", "                csv_files_found += 1\n", "            else:\n", "                print(f\" No data extracted or error occurred for {filename}.\")\n", "\n", "    return all_budget_df\n", "\n", "def bronze_expenses()-> pd.DataFrame:\n", "    \"\"\"Returns a DataFrame containing all expenses from all DBs.\"\"\"\n", "    # --- Bronze Layer: Ingesting Expenses from SQLite DBs ---\n", "    def extract_expenses_from_db(db_path: str) -> pd.DataFrame:\n", "        \"\"\"\n", "        Connects to a SQLite database and extracts all data from the 'expenses' table.\n", "        \"\"\"\n", "        conn = None\n", "        try:\n", "            conn = sqlite3.connect(db_path)\n", "            query = \"SELECT * FROM expenses\" # Assumes 'expenses' is the table name\n", "            df = pd.read_sql_query(query, conn)\n", "            return df\n", "        except sqlite3.Error as e:\n", "            print(f\"Error reading expenses from {db_path}: {e}\")\n", "            return pd.DataFrame() # Return empty DataFrame on error\n", "        finally:\n", "            if conn:\n", "                conn.close()\n", "\n", "\n", "    # --- Main logic to iterate and combine DBs ---\n", "    all_expenses_df = pd.DataFrame()\n", "    db_files_found = 0\n", "\n", "    # Define project currency map for expenses\n", "    PROJECT_CURRENCY_MAP = {\n", "        \"BE01\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "        \"BE55\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "        \"KE01\": {\"country\": \"Kenya\", \"currency\": \"KES\"},\n", "        \"KE02\": {\"country\": \"Kenya\", \"currency\": \"KES\"},   \n", "        \"SN01\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "        \"SN02\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "        \"BF01\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "        \"BF02\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "    }\n", "\n", "    print(f\"\\n--- Bronze Layer: Starting DB Data Ingestion from {DATA_DIRECTORY} ---\")\n", "\n", "    if not os.path.exists(DATA_DIRECTORY):\n", "        print(f\"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.\")\n", "    else:\n", "        for filename in os.listdir(DATA_DIRECTORY):\n", "            file_path = os.path.join(DATA_DIRECTORY, filename)\n", "            \n", "            # Process only files ending with '.db' and skip directories\n", "            if os.path.isdir(file_path) or not filename.endswith(\".db\"):\n", "                continue\n", "\n", "            # Derive project_id from filename (e.g., \"BE01\" from \"BE01.db\")\n", "            project_name_from_file = os.path.splitext(filename)[0]\n", "            base_project_id = project_name_from_file # For DBs, the filename directly is the project_id\n", "\n", "            # Get country and currency from our PROJECT_CURRENCY_MAP\n", "            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)\n", "            if not project_info:\n", "                print(f\"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.\")\n", "                continue\n", "            \n", "            country_from_map = project_info['country']\n", "            currency_from_map = project_info['currency'] # This will be our source of truth for currency\n", "\n", "            print(f\"  Ingesting expenses from DB: {filename} (Project: {base_project_id}, Country: {country_from_map})...\")\n", "            df = extract_expenses_from_db(file_path)\n", "            \n", "            if not df.empty:\n", "                df['project_id'] = base_project_id\n", "                df['country'] = country_from_map\n", "                df['original_currency'] = currency_from_map # Add the true currency from our map\n", "                all_expenses_df = pd.concat([all_expenses_df, df], ignore_index=True)\n", "                db_files_found += 1\n", "            else:\n", "                print(f\"   No data extracted or error occurred for {filename}.\")\n", "\n", "    return all_expenses_df"]}, {"cell_type": "code", "execution_count": 4, "id": "f156fcbf", "metadata": {}, "outputs": [], "source": ["# USe the bronze_layer to get expenses df and budget df\n", "import pandas as pd\n", "import sqlite3\n", "import os\n", "from bronze_layer import bronze_expenses, bronze_budget\n", "from pandas.tseries.offsets import MonthEnd\n", "\n", "DATA_DIRECTORY = \"/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025\"\n", "OUTPUT_DIRECTORY = \"/Users/<USER>/test/MSF-test/processed_data\"\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b392139f", "metadata": {}, "outputs": [], "source": ["def silver_budget() -> pd.DataFrame:\n", "    \"\"\"Returns a clean budget dataframe.\"\"\"\n", "    budget_df = bronze_budget()\n", "    # modify KEO1 to KE01 in bronze budget df\n", "    budget_df['project_id'] = budget_df['project_id'].replace('KEO1', 'KE01')\n", "\n", "    # drop version column, id column\n", "    budget_df.drop(columns=['version', 'id'], inplace=True)\n", "\n", "    # Create a date column:\n", "    # convert the 'year' and 'month' columns to string format\n", "    budget_df['year'] = budget_df['year'].astype(str)\n", "    budget_df['month'] = budget_df['month'].astype(str)\n", "\n", "    # create a temporary date string with the 1st day of the month\n", "    budget_df['temp_date_str'] = budget_df['year'] + '-' + budget_df['month'].str.zfill(2) + '-01'\n", "\n", "    # convert the temporary date string to datetime objects\n", "    budget_df['date'] = pd.to_datetime(budget_df['temp_date_str'], errors='coerce')\n", "\n", "    # convert to the last day of the month using MonthEnd\n", "    budget_df['date'] = budget_df['date'] + MonthEnd(1)\n", "\n", "    # drop the intermediate columns \n", "    budget_df = budget_df.drop(columns=['year', 'month', 'temp_date_str'])\n", "\n", "    return budget_df"]}, {"cell_type": "code", "execution_count": 6, "id": "9fe96f95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Bronze Layer: Starting CSV Data Ingestion from /Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025 ---\n", "project_name_from_file: BE55_budget\n", "  Ingesting budget from CSV: BE55_budget.csv (Project: BE55, Country: Belgium)...\n", "project_name_from_file: SN02_budget\n", "  Ingesting budget from CSV: SN02_budget.csv (Project: SN02, Country: Senegal)...\n", "project_name_from_file: BF01_budget\n", "  Ingesting budget from CSV: BF01_budget.csv (Project: BF01, Country: Burkina Faso)...\n", "project_name_from_file: BE01_budget\n", "  Ingesting budget from CSV: BE01_budget.csv (Project: BE01, Country: Belgium)...\n", "project_name_from_file: BF02_budget\n", "  Ingesting budget from CSV: BF02_budget.csv (Project: BF02, Country: Burkina Faso)...\n", "project_name_from_file: SN01_budget\n", "  Ingesting budget from CSV: SN01_budget.csv (Project: SN01, Country: Senegal)...\n", "project_name_from_file: KE01_budget\n", "  Ingesting budget from CSV: KE01_budget.csv (Project: KE01, Country: Kenya)...\n", "project_name_from_file: KEO2_budget\n", "  Ingesting budget from CSV: KEO2_budget.csv (Project: KEO2, Country: Kenya)...\n"]}], "source": ["silver_budget_df = silver_budget()"]}, {"cell_type": "code", "execution_count": 7, "id": "390b9a3c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 6 columns):\n", " #   Column      Non-Null Count  Dtype         \n", "---  ------      --------------  -----         \n", " 0   department  3456 non-null   object        \n", " 1   category    3456 non-null   object        \n", " 2   budget_eur  3456 non-null   float64       \n", " 3   project_id  3456 non-null   object        \n", " 4   country     3456 non-null   object        \n", " 5   date        3456 non-null   datetime64[ns]\n", "dtypes: datetime64[ns](1), float64(1), object(4)\n", "memory usage: 162.1+ KB\n"]}], "source": ["silver_budget_df.info()"]}, {"cell_type": "code", "execution_count": 8, "id": "fa7cf7d3", "metadata": {}, "outputs": [{"data": {"text/plain": ["department    0\n", "category      0\n", "budget_eur    0\n", "project_id    0\n", "country       0\n", "date          0\n", "dtype: int64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["silver_budget_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 9, "id": "98b92251", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Bronze Layer: Starting DB Data Ingestion from /Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025 ---\n", "  Ingesting expenses from DB: BE55.db (Project: BE55, Country: Belgium)...\n", "  Ingesting expenses from DB: BE01.db (Project: BE01, Country: Belgium)...\n", "  Ingesting expenses from DB: BF02.db (Project: BF02, Country: Burkina Faso)...\n", "  Ingesting expenses from DB: KE02.db (Project: KE02, Country: Kenya)...\n", "  Ingesting expenses from DB: SN02.db (Project: SN02, Country: Senegal)...\n", "  Ingesting expenses from DB: SN01.db (Project: SN01, Country: Senegal)...\n", "  Ingesting expenses from DB: KE01.db (Project: KE01, Country: Kenya)...\n", "  Ingesting expenses from DB: BF01.db (Project: BF01, Country: Burkina Faso)...\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 11 columns):\n", " #   Column             Non-Null Count  Dtype  \n", "---  ------             --------------  -----  \n", " 0   id                 3456 non-null   int64  \n", " 1   year               3456 non-null   int64  \n", " 2   month              3456 non-null   object \n", " 3   department         3456 non-null   object \n", " 4   category           3456 non-null   object \n", " 5   amount_local       2880 non-null   float64\n", " 6   currency           3456 non-null   object \n", " 7   project_id         3456 non-null   object \n", " 8   country            3456 non-null   object \n", " 9   original_currency  3456 non-null   object \n", " 10  amount_eur         432 non-null    float64\n", "dtypes: float64(2), int64(2), object(7)\n", "memory usage: 297.1+ KB\n"]}], "source": ["# Manipulating to get silver_expenses_df\n", "expenses_df = bronze_expenses()\n", "\n", "expenses_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "6bac8fab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 8 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   department         3456 non-null   object        \n", " 1   category           3456 non-null   object        \n", " 2   amount_local       2880 non-null   float64       \n", " 3   project_id         3456 non-null   object        \n", " 4   country            3456 non-null   object        \n", " 5   original_currency  3456 non-null   object        \n", " 6   amount_eur         432 non-null    float64       \n", " 7   date               3456 non-null   datetime64[ns]\n", "dtypes: datetime64[ns](1), float64(2), object(5)\n", "memory usage: 216.1+ KB\n"]}], "source": ["# Drop id and currency columns\n", "expenses_df.drop(columns=['id', 'currency'], inplace=True)\n", "\n", "# Create a date column:\n", "# convert the 'year' and 'month' columns to string format\n", "expenses_df['year'] = expenses_df['year'].astype(str)\n", "expenses_df['month'] = expenses_df['month'].astype(str)\n", "\n", "# create a temporary date string with the 1st day of the month\n", "expenses_df['temp_date_str'] = expenses_df['year'] + '-' + expenses_df['month'].str.zfill(2) + '-01'\n", "\n", "# convert the temporary date string to datetime objects\n", "expenses_df['date'] = pd.to_datetime(expenses_df['temp_date_str'], errors='coerce')\n", "\n", "# convert to the last day of the month using MonthEnd\n", "expenses_df['date'] = expenses_df['date'] + MonthEnd(1)\n", "\n", "# drop the intermediate columns \n", "expenses_df = expenses_df.drop(columns=['year', 'month', 'temp_date_str'])\n", "\n", "# create rate column\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "46ef7199", "metadata": {}, "outputs": [], "source": ["# Drop amount_eur column\n", "expenses_df.drop(columns=['amount_eur'], inplace=True)\n", "\n", "expenses_df.info()"]}, {"cell_type": "code", "execution_count": 11, "id": "d41d34b9", "metadata": {}, "outputs": [], "source": ["# Define our rate map\n", "from get_latest_exchange_rate import get_latest_exchange_rate\n", "\n", "eur_to_eur_rate = 1.0 # EUR to EUR conversion rate is 1\n", "kes_to_eur_rate = get_latest_exchange_rate(\"KES\", \"EUR\")\n", "xof_to_eur_rate = get_latest_exchange_rate(\"XOF\", \"EUR\")\n", "\n", "rate_mapping = {\n", "    'EUR': eur_to_eur_rate,\n", "    'KES': kes_to_eur_rate,\n", "    'XOF': xof_to_eur_rate\n", "}\n", "\n", "# Add rate column\n", "expenses_df['rate'] = expenses_df['original_currency'].map(rate_mapping)\n", "\n", "# Add amount_eur column\n", "expenses_df['amount_eur'] = expenses_df['amount_local'] * expenses_df['rate']\n"]}, {"cell_type": "code", "execution_count": 12, "id": "b0f93d4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 9 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   department         3456 non-null   object        \n", " 1   category           3456 non-null   object        \n", " 2   amount_local       2880 non-null   float64       \n", " 3   project_id         3456 non-null   object        \n", " 4   country            3456 non-null   object        \n", " 5   original_currency  3456 non-null   object        \n", " 6   amount_eur         2880 non-null   float64       \n", " 7   date               3456 non-null   datetime64[ns]\n", " 8   rate               3456 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(3), object(5)\n", "memory usage: 243.1+ KB\n"]}], "source": ["expenses_df.info()"]}, {"cell_type": "code", "execution_count": 15, "id": "8fc85e14", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(576)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df[\"amount_eur\"].isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "bd91ef96", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "798d06ac", "metadata": {}, "outputs": [], "source": ["silver_expenses_df = expenses_df"]}, {"cell_type": "code", "execution_count": 18, "id": "541e4df6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 9 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   department         3456 non-null   object        \n", " 1   category           3456 non-null   object        \n", " 2   amount_local       2880 non-null   float64       \n", " 3   project_id         3456 non-null   object        \n", " 4   country            3456 non-null   object        \n", " 5   original_currency  3456 non-null   object        \n", " 6   amount_eur         2880 non-null   float64       \n", " 7   date               3456 non-null   datetime64[ns]\n", " 8   rate               3456 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(3), object(5)\n", "memory usage: 243.1+ KB\n"]}], "source": ["silver_expenses_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "12a35f3a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "250ddef7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b561d71a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "052eaacf", "metadata": {}, "source": ["# GOLD LAYER"]}, {"cell_type": "code", "execution_count": 2, "id": "999c6064", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import os\n", "from silver_layer import silver_budget, silver_expenses\n", "\n", "# def gold():\n", "#     \"\"\"Returns a set of gold dataframes from the silver dataframes\"\"\"\n", "#     silver_budget_df = silver_budget()\n", "#     silver_expenses_df = silver_expenses()"]}, {"cell_type": "code", "execution_count": 3, "id": "1b8cf80a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Bronze Layer: Starting CSV Data Ingestion from /Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025 ---\n", "project_name_from_file: BE55_budget\n", "  Ingesting budget from CSV: BE55_budget.csv (Project: BE55, Country: Belgium)...\n", "project_name_from_file: SN02_budget\n", "  Ingesting budget from CSV: SN02_budget.csv (Project: SN02, Country: Senegal)...\n", "project_name_from_file: BF01_budget\n", "  Ingesting budget from CSV: BF01_budget.csv (Project: BF01, Country: Burkina Faso)...\n", "project_name_from_file: BE01_budget\n", "  Ingesting budget from CSV: BE01_budget.csv (Project: BE01, Country: Belgium)...\n", "project_name_from_file: BF02_budget\n", "  Ingesting budget from CSV: BF02_budget.csv (Project: BF02, Country: Burkina Faso)...\n", "project_name_from_file: SN01_budget\n", "  Ingesting budget from CSV: SN01_budget.csv (Project: SN01, Country: Senegal)...\n", "project_name_from_file: KE01_budget\n", "  Ingesting budget from CSV: KE01_budget.csv (Project: KE01, Country: Kenya)...\n", "project_name_from_file: KEO2_budget\n", "  Ingesting budget from CSV: KEO2_budget.csv (Project: KEO2, Country: Kenya)...\n", "\n", "--- Bronze Layer: Starting DB Data Ingestion from /Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025 ---\n", "  Ingesting expenses from DB: BE55.db (Project: BE55, Country: Belgium)...\n", "  Ingesting expenses from DB: BE01.db (Project: BE01, Country: Belgium)...\n", "  Ingesting expenses from DB: BF02.db (Project: BF02, Country: Burkina Faso)...\n", "  Ingesting expenses from DB: KE02.db (Project: KE02, Country: Kenya)...\n", "  Ingesting expenses from DB: SN02.db (Project: SN02, Country: Senegal)...\n", "  Ingesting expenses from DB: SN01.db (Project: SN01, Country: Senegal)...\n", "  Ingesting expenses from DB: KE01.db (Project: KE01, Country: Kenya)...\n", "  Ingesting expenses from DB: BF01.db (Project: BF01, Country: Burkina Faso)...\n"]}], "source": ["silver_budget_df = silver_budget()\n", "silver_expenses_df = silver_expenses()"]}, {"cell_type": "code", "execution_count": 22, "id": "308f8564", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 6 columns):\n", " #   Column      Non-Null Count  Dtype         \n", "---  ------      --------------  -----         \n", " 0   department  3456 non-null   object        \n", " 1   category    3456 non-null   object        \n", " 2   budget_eur  3456 non-null   float64       \n", " 3   project_id  3456 non-null   object        \n", " 4   country     3456 non-null   object        \n", " 5   date        3456 non-null   datetime64[ns]\n", "dtypes: datetime64[ns](1), float64(1), object(4)\n", "memory usage: 162.1+ KB\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 9 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   department         3456 non-null   object        \n", " 1   category           3456 non-null   object        \n", " 2   amount_local       2880 non-null   float64       \n", " 3   project_id         3456 non-null   object        \n", " 4   country            3456 non-null   object        \n", " 5   original_currency  3456 non-null   object        \n", " 6   date               3456 non-null   datetime64[ns]\n", " 7   rate               3456 non-null   float64       \n", " 8   amount_eur         2880 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(3), object(5)\n", "memory usage: 243.1+ KB\n"]}], "source": ["silver_budget_df.info()\n", "silver_expenses_df.info()"]}, {"cell_type": "code", "execution_count": 5, "id": "ed1a1dcd", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['BE55', 'SN02', 'BF01', 'BE01', 'BF02', 'SN01', 'KE01', 'KE02'],\n", "      dtype=object)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["silver_budget_df['project_id'].unique()"]}, {"cell_type": "code", "execution_count": 6, "id": "5bdd7c17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["# check if set of project id in expenses == set of project id in budget\n", "print(set(silver_expenses_df['project_id'].unique()) == set(silver_budget_df['project_id'].unique()))"]}, {"cell_type": "code", "execution_count": 8, "id": "c689f117", "metadata": {}, "outputs": [], "source": ["# # correct the issue above\n", "# # inspect first\n", "# print(set(silver_expenses_df['project_id'].unique()))\n", "# print(set(silver_budget_df['project_id'].unique()))"]}, {"cell_type": "code", "execution_count": 9, "id": "f2f88300", "metadata": {}, "outputs": [], "source": ["# combine the silver_budget_df and silver_expenses_df\n", "# with common columns: project_id, date, country, department, category"]}, {"cell_type": "code", "execution_count": 10, "id": "23b29139", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 10 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   department         3456 non-null   object        \n", " 1   category           3456 non-null   object        \n", " 2   budget_eur         3456 non-null   float64       \n", " 3   project_id         3456 non-null   object        \n", " 4   country            3456 non-null   object        \n", " 5   date               3456 non-null   datetime64[ns]\n", " 6   amount_local       2880 non-null   float64       \n", " 7   original_currency  3456 non-null   object        \n", " 8   rate               3456 non-null   float64       \n", " 9   amount_eur         2880 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(4), object(5)\n", "memory usage: 270.1+ KB\n"]}], "source": ["# Merge the silver_budget_df and silver_expenses_df\n", "common_columns = ['date', 'project_id', 'country', 'department', 'category']\n", "\n", "merged_df = pd.merge(silver_budget_df, silver_expenses_df, on=common_columns, how='inner')  # inner is default\n", "merged_df.info()"]}, {"cell_type": "code", "execution_count": 11, "id": "a74918b6", "metadata": {}, "outputs": [], "source": ["# Create a snowflake schema from the merged_df"]}, {"cell_type": "code", "execution_count": 12, "id": "68299b84", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DimDepartment head:\n", "   department_key department_name\n", "0               1              HR\n", "1               2         Medical\n", "2               3       Logistics\n", "3               4          Supply\n", "\n", "\n"]}], "source": ["# DimDepartment\n", "# Extract unique departments and create a surrogate key\n", "dim_department = merged_df[['department']].drop_duplicates().reset_index(drop=True)\n", "dim_department['department_key'] = dim_department.index + 1 # Start keys from 1\n", "dim_department = dim_department[['department_key', 'department']] # Reorder columns\n", "dim_department.rename(columns={'department': 'department_name'}, inplace=True)\n", "print(\"DimDepartment head:\")\n", "print(dim_department.head())\n", "print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 13, "id": "9b5615f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DimCategory head:\n", "   category_key      category_name\n", "0             1           Salaries\n", "1             2           Training\n", "2             3        Recruitment\n", "3             4        Medications\n", "4             5  Medical Equipment\n", "\n", "\n"]}], "source": ["# DimCategory\n", "dim_category = merged_df[['category']].drop_duplicates().reset_index(drop=True)\n", "dim_category['category_key'] = dim_category.index + 1\n", "dim_category = dim_category[['category_key', 'category']]\n", "dim_category.rename(columns={'category': 'category_name'}, inplace=True)\n", "print(\"DimCategory head:\")\n", "print(dim_category.head())\n", "print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 14, "id": "799d443e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DimProject head:\n", "   project_key project_name\n", "0            1         BE55\n", "1            2         SN02\n", "2            3         BF01\n", "3            4         BE01\n", "4            5         BF02\n", "\n", "\n"]}], "source": ["# DimProject\n", "dim_project = merged_df[['project_id']].drop_duplicates().reset_index(drop=True)\n", "dim_project['project_key'] = dim_project.index + 1\n", "dim_project = dim_project[['project_key', 'project_id']]\n", "dim_project.rename(columns={'project_id': 'project_name'}, inplace=True) # Assuming project_id is also the name\n", "print(\"DimProject head:\")\n", "print(dim_project.head())\n", "print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 16, "id": "fd8101dd", "metadata": {}, "outputs": [], "source": ["# convert the merged df to csv\n", "merged_df.to_csv('/Users/<USER>/test/MSF-test2/processed_data/merged_df.csv', index=False)"]}, {"cell_type": "code", "execution_count": 17, "id": "b6538b41", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 10 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   department         3456 non-null   object        \n", " 1   category           3456 non-null   object        \n", " 2   budget_eur         3456 non-null   float64       \n", " 3   project_id         3456 non-null   object        \n", " 4   country            3456 non-null   object        \n", " 5   date               3456 non-null   datetime64[ns]\n", " 6   amount_local       2880 non-null   float64       \n", " 7   original_currency  3456 non-null   object        \n", " 8   rate               3456 non-null   float64       \n", " 9   amount_eur         2880 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(4), object(5)\n", "memory usage: 270.1+ KB\n"]}], "source": ["merged_df.info()"]}, {"cell_type": "code", "execution_count": 18, "id": "fa43fd6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 10 columns):\n", " #   Column             Non-Null Count  Dtype  \n", "---  ------             --------------  -----  \n", " 0   department         3456 non-null   object \n", " 1   category           3456 non-null   object \n", " 2   budget_eur         3456 non-null   float64\n", " 3   project_id         3456 non-null   object \n", " 4   country            3456 non-null   object \n", " 5   date               3456 non-null   object \n", " 6   amount_local       2880 non-null   float64\n", " 7   original_currency  3456 non-null   object \n", " 8   rate               3456 non-null   float64\n", " 9   amount_eur         2880 non-null   float64\n", "dtypes: float64(4), object(6)\n", "memory usage: 270.1+ KB\n"]}], "source": ["df = pd.read_csv('/Users/<USER>/test/MSF-test2/processed_data/merged_df.csv')\n", "\n", "df.info()"]}, {"cell_type": "code", "execution_count": 19, "id": "d6a77867", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['HR', 'Medical', 'Logistics', 'Supply'], dtype=object)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df['department'].unique()"]}, {"cell_type": "code", "execution_count": 20, "id": "1a9c5a84", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Salaries', 'Training', 'Recruitment', 'Medications',\n", "       'Medical Equipment', 'Consultations', 'Fuel', 'Transport',\n", "       'Vehicle Maintenance', 'Supplies', 'Warehousing', 'Cold Chain'],\n", "      dtype=object)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df['category'].unique()"]}, {"cell_type": "code", "execution_count": 22, "id": "06d42874", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df['category'].nunique()"]}, {"cell_type": "code", "execution_count": 23, "id": "bcf7e794", "metadata": {}, "outputs": [{"data": {"text/plain": ["36"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df['date'].nunique()"]}, {"cell_type": "code", "execution_count": 24, "id": "54e959db", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Belgium', 'Senegal', 'Burkina Faso', 'Kenya'], dtype=object)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df['country'].unique()  "]}, {"cell_type": "code", "execution_count": 25, "id": "6ac5a8b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['BE55', 'SN02', 'BF01', 'BE01', 'BF02', 'SN01', 'KE01', 'KE02'],\n", "      dtype=object)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df['project_id'].unique()"]}, {"cell_type": "code", "execution_count": 29, "id": "56415500", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df['rate'].nunique()"]}, {"cell_type": "code", "execution_count": 30, "id": "20445586", "metadata": {}, "outputs": [{"data": {"text/plain": ["2878"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df['amount_local'].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "f90f5454", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}